import React, { ReactNode, useState } from "react";

type AgentNavigateProps = {
  chatTab: ReactNode,
  ticketTab: ReactNode,
  workspaceTab?: ReactNode
}
const AgentNavigate: React.FC<AgentNavigateProps> = ({ chatTab, ticketTab, workspaceTab = null }) => {
  const [tab, setTab] = useState("chat")
  const onClickTab = (value: string) => {
    setTab(value)
  }
  return (
    <div>
      <div className="w-full p-2">
        {/* Tabs */}
        <div className="flex gap-1">
          <button className={`px-4 py-2 ${tab == 'chat' ? 'bg-gradient-secondary text-white' : 'text-sky-600 hover:text-sky-700'} font-semibold rounded-t-md transition-all duration-300`} onClick={() => onClickTab('chat')}>
            My Chat
          </button>
          <button className={`px-4 py-2 ${tab == 'ticket' ? 'bg-gradient-secondary text-white' : 'text-sky-600 hover:text-sky-700'} font-semibold rounded-t-md transition-all duration-300`} onClick={() => onClickTab('ticket')}>
            My tickets
          </button>
          {workspaceTab &&
          <button className={`px-4 py-2 ${tab == 'workspace' ? 'bg-gradient-secondary text-white' : 'text-sky-600 hover:text-sky-700'} font-semibold rounded-t-md transition-all duration-300`} onClick={() => onClickTab('workspace')}>
            Workspace
          </button>
          }
        </div>

        {/* Breadcrumb */}
        {/* <div className="bg-blue-100 px-4 py-3 text-sm text-blue-900">
        <span className="text-gray-600">Dashboard</span> &gt;&nbsp;
        <span className="text-gray-600">SCS Supports</span> &gt;&nbsp;
        <span className="font-semibold text-blue-900">Chat</span>
      </div> */}
      </div>
      {
        tab == 'chat' ? chatTab : tab == 'ticket' ? ticketTab : workspaceTab
      }
    </div>

  );
};

export default AgentNavigate;