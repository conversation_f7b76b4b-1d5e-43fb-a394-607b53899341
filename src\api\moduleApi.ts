import axiosClient from '@/lib/axiosClient';
import { ModuleApp, ModuleAppPagination } from '@/types/index';

export const moduleApi = {
  async getModules(enabled: boolean = true, limit: number = 10, offset: number = 0, signal?: AbortSignal): Promise<ModuleApp[]> {
    const res = await axiosClient.get('/v1/module/get-modules', { params: { enabled: enabled, limit: limit, offset: offset }, signal});
    return res.data.data;
  },
  async getModulePagination(enabled: boolean = true,limit: number = 10, offset: number = 0, signal?: AbortSignal): Promise<ModuleAppPagination>{
    const res = await axiosClient.get('/v1/module/get-modules', { params: { enabled: enabled, limit: limit, offset: offset }, signal});
    return res.data;
  },
  async addModule(moduleName: string, description: string, signal?: AbortSignal): Promise<number>{
    const res = await axiosClient.post('/v1/module/add-module', JSON.stringify({ moduleName: moduleName, description: description }), {signal});
    return res.data.data;
  },
  async updateModule(id: number, moduleName: string, description: string, enabled: boolean, signal?: AbortSignal): Promise<boolean>{
    const res = await axiosClient.post('/v1/module/update-module', JSON.stringify({id: id, moduleName: moduleName, description: description, enabled: enabled }), {signal});
    return res.data.data;
  }
};