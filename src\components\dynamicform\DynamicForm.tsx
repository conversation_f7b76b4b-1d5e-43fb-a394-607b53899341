"use client";
import { useState, ChangeEvent, FormEvent } from "react";

// Định nghĩa type cho từng trường form
export type FieldType = "text" | "number" | "date" | "email" | "password" | "select" | "textarea" | "file"; // có thể mở rộng nếu cần

export interface FieldOption {
  label: string;
  value: string | number;
}

export interface FieldConfig {
  label: string;
  name: string;
  type: FieldType;
  value?: string | number;
  placeholder?: string;
  options?: FieldOption[]; // dùng cho select, radio
}

export interface DynamicFormProps {
  fields: FieldConfig[];
  onSubmit?: (form: Record<string, string | number>) => void;
}

export default function DynamicForm({ fields = [], onSubmit }: DynamicFormProps) {
  const [form, setForm] = useState<Record<string, string | number>>(
    fields.reduce((acc, cur) => ({ ...acc, [cur.name]: cur.value || "" }), {})
  );

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setForm((f) => ({
      ...f,
      [name]: type === "number" ? Number(value) : value,
    }));
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onSubmit?.(form);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="max-w-xl mx-auto p-6 bg-white rounded-lg shadow-md space-y-6"
    >
      <h2 className="text-center text-xl font-bold mb-4">Thông tin xác nhận hồ sơ</h2>
      {fields.map((field) => (
        <div key={field.name}>
          <label
            htmlFor={field.name}
            className="block mb-1 font-semibold text-gray-700 cursor-pointer"
          >
            {field.label}
          </label>
          {field.type === "textarea" ? (
            <textarea
              name={field.name}
              id={field.name}
              value={form[field.name]}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-300 outline-none transition"
              placeholder={field.placeholder || `Nhập ${field.label.toLowerCase()}`}
            />
          ) : field.type === "select" && field.options ? (
            <select
              name={field.name}
              id={field.name}
              value={form[field.name]}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-300 outline-none transition"
            >
              <option value="">Chọn {field.label.toLowerCase()}</option>
              {field.options.map((opt) => (
                <option key={opt.value} value={opt.value}>
                  {opt.label}
                </option>
              ))}
            </select>
          ) : (
            <input
              type={field.type}
              name={field.name}
              id={field.name}
              value={form[field.name]}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-300 outline-none transition"
              placeholder={field.placeholder || `Nhập ${field.label.toLowerCase()}`}
              autoComplete="off"
            />
          )}
        </div>
      ))}
      <button
        type="submit"
        className="w-full py-3 rounded-md bg-gradient-to-r from-blue-600 to-blue-400 text-white font-semibold text-lg shadow-md hover:from-blue-700 hover:to-blue-500 transition"
      >
        Hoàn tất
      </button>
    </form>
  );
}
