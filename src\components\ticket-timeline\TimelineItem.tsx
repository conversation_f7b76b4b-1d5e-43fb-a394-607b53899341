import { TicketStatus } from "@/enums/AgentCode";
import { TicketApprovalStatus } from "@/types";

type TimelineItemProps = {
  data: TicketApprovalStatus
  end: boolean;
};

const statusColors: Record<string, string> = {
  'Approving' : 'bg-yellow-500',
  'Approved': 'bg-green-500',
  'Reject': 'bg-red-500',
  'Todo': 'bg-gray-500'
};

export const TimelineItem = ({ data, end }: TimelineItemProps) => {
  return (
    <div className="flex gap-3 mb-4 w-full">
      {/* Cột trái: Dot + Line */}
      <div className="flex flex-col items-center">
        <div className={`w-3 h-3 ${statusColors[data.status]} rounded-full mt-1.5`} />
        {!end && <div className="w-px flex-1 bg-gray-300 mt-2" />}
      </div>

      {/* Nội dung */}
      <div className="flex justify-between b-2 w-full">
        
        <div className="flex-1">
          <div className="text-sm text-gray-900 font-medium mb-1">{data.name}</div>
          <div className="text-xs text-gray-500 mb-1">
            {data.updatedAt ? new Date(data.updatedAt).toLocaleString() : new Date(data.createdAt).toLocaleString()}
          </div>
          <div className="text-xs text-gray-600">
            {data.userId}
          </div>
        </div>
        {
          data.approvedBy && (
            <div className="">
              <div className="text-sm text-gray-900 font-medium mb-1 flex justify-end">Người duyệt</div>
              <div className="text-xs text-gray-600">{data.approvedBy}</div>
            </div>
          )
        }
       
      </div>
    </div>
  );
};
