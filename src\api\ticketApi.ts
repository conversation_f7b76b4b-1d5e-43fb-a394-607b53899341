import axiosClient from '@/lib/axiosClient';
import { Conversation, ChatMessage, Ticket, FileItem, TicketPagination } from '@/types/index';

export const ticketApi = {
  async getTickets(moduleId?: number, title?: string, status?: string, limit: number = 10, offset: number = 0, signal?: AbortSignal): Promise<Ticket[]> {
    const params: any = {};
    if (moduleId !== undefined && moduleId !== 0) {
      params.moduleId = moduleId;
    }
    if (title) params.title = title;
    if (status) params.status = status;
     if (limit) params.limit = limit;
    if (offset) params.offset = offset;
    
    const res = await axiosClient.get('/v1/ticket/get-tickets', { params, signal });
    return res.data.data;
  },

  async getTicketPagination(moduleId?: number, title?: string, status?: string, limit?: number, offset?: number, signal?: AbortSignal): Promise<TicketPagination> {
    const params: any = {};
    if (moduleId) params.moduleId = moduleId;
    if (title) params.title = title;
    if (status) params.status = status;
    if (limit) params.limit = limit;
    if (offset) params.offset = offset;

    const res = await axiosClient.get('/v1/ticket/get-tickets', { params, signal });
    return res.data;
  },

  async getTicketDetail(ticketId: number, signal?: AbortSignal): Promise<Ticket> {
    const res = await axiosClient.get('/v1/ticket/get-ticket-detail', { params: { ticketid: ticketId }, signal })
    return res.data.data;
  },

  async getTicketFiles(ticketId: number, signal?: AbortSignal): Promise<FileItem[]> {
    const res = await axiosClient.get('/v1/ticket/get-file-ticket', { params: { ticketid: ticketId }, signal })
    return res.data.data;
  },

  async getTicketAction(ticketId: number, signal?: AbortSignal): Promise<boolean> {
    const res = await axiosClient.get('/v1/ticket/get-ticket-action', { params: { ticketid: ticketId }, signal })
    return res.data.data;
  },

  async updateTicketStatus(ticketId: number, stepId: number, status: number, signal?: AbortSignal): Promise<number> {
    const res = await axiosClient.post('/v1/ticket/update-ticket-status', JSON.stringify({ ticketId: ticketId, stepId: stepId, status: status }), { signal })
    return res.data.data
  },

  async uploadFile(ticketId: number, files: File[], signal? : AbortSignal): Promise<boolean>{
     const formData = new FormData();

      formData.append('ticketId', ticketId.toString());

      files.forEach(file => {
        formData.append('Files', file);
      });

      const res = await axiosClient.post('/v1/ticket/upload-file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        signal
      });

      return res.data.data;
  } 

  
};