
"use client";
import { useEffect, useRef, useState } from 'react';
import axios from 'axios';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { ticketApi } from '@/api/ticketApi';
import { Conversation, FileItem, Ticket, TicketDetail } from '@/types';
import { TicketRequestStatus, TicketStatus } from '@/enums/AgentCode';
import { FaDownload, FaHistory, FaUpload } from 'react-icons/fa';
import {Timeline} from '@/components/ticket-timeline/Timeline';
import { fileApi } from '@/api/fileApi';
import JSZip from 'jszip';
import ChatWidget from '@/components/agent/AgentChatWidget';
import { conversationApi } from '@/api/conversationApi';

const statusClass: Record<string, string> = {
  'Đã đóng': 'bg-gray-100 text-gray-800',
  'Bị từ chối': 'bg-red-100 text-red-800',
  '<PERSON><PERSON> xử lý': 'bg-yellow-100 text-yellow-800',
};

export default function TicketDetailPage() {
  const { id } = useParams();
  const [data, setData] = useState<TicketDetail | null>(null);
  const [files, setFiles] = useState<FileItem[]>([])
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [actions, setActions] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'info' | 'files' | 'additional'>('info');
  const [activeStepTab, setActiveStepTab] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const getOverallStatus = (ticketDetail: TicketDetail): string => {
    if (ticketDetail == null || ticketDetail.ticketStatuses == null)
      return ""
    const hasReject = ticketDetail.ticketStatuses.some(
      status => status.status === TicketStatus.Reject
    )

    if (hasReject) return "Bị từ chối"

    const hasApproving = ticketDetail.ticketStatuses.some(
      status => status.status === TicketStatus.Approving
    );

    if (hasApproving) return "Đang xử lý"

    return "Đã đóng"
  }

  const handleApproval = async () => {
    console.log(1)
    const currentStep = data?.ticketStatuses.filter(
      ticket => ticket.status === TicketStatus.Approving
    );

    if(!currentStep || !id)
      return

    const updateTicketStatus = await ticketApi.updateTicketStatus(Number(id), currentStep[0].id, TicketRequestStatus.Approved)
    if(updateTicketStatus)
    {
      fetchData();
    }
  }

  const handleReject = async () => {
    const currentStep = data?.ticketStatuses.filter(
      ticket => ticket.status === TicketStatus.Approving
    );

    if(!currentStep || !id)
      return

    const updateTicketStatus = await ticketApi.updateTicketStatus(Number(id), currentStep[0].id, TicketRequestStatus.Reject)
    if(updateTicketStatus)
    {
      fetchData();
    }
  }

  const getTicketActions = async () => {
    try {
      const actions = await ticketApi.getTicketAction(Number(id))
      if(actions == null)
      {
        setActions(false)
      }
      setActions(actions)
    } catch (err) {
      console.log(err)
    }
  }
  const fetchData = async () => {
    try {
      const ticket = await ticketApi.getTicketDetail(Number(id))
      setData(ticket)
      
      if(ticket != null){
        await getTicketActions();
      }
    } catch (err) {
      console.log(err)
    }
  }
  const fetchConversation = async () => {
    try {
      const response = await conversationApi.getConversationsByTicket(Number(id))
      if(response){
        setConversation(response[0])
      }
    } catch (err) {
      setFiles([])
      console.log(err)
    }
  }

  const fetchFiles = async () => {
    try {
      const fileList = await ticketApi.getTicketFiles(Number(id))
      setFiles(fileList)
    } catch (err) {
      setFiles([])
      console.log(err)
    }
  }

  useEffect(() => {
    if (id) {
      fetchData();
      fetchConversation();
      fetchFiles();
    }
  }, [id]);

  const downloadFile = async (ticketId: number ,file: FileItem) => {
    if (file.id)
    {
      const downloadLink = await fileApi.getTicketSignedUrl(ticketId, file.id)
      window.open(downloadLink,  "_blank")
    }
  }
  const uploadFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if(data){
      const selectedFiles = e.target.files;
      if (selectedFiles) {
          var res = await ticketApi.uploadFile(data.id, [...selectedFiles])
          if(res)
          {
            fetchFiles()
          }
      }
      if (fileInputRef.current) {
          fileInputRef.current.value = "";
      }
    }
  }

  const groupFilesByFolder = (files: FileItem[]) => {
    const grouped: Record<string, any> = {};

    files.forEach((file) => {
      const parts = file.fileName.split('/');
      const nameOnly = parts.pop() || '';
      const folderPath = parts.join('/') || 'root';

      if (!grouped[folderPath]) {
        grouped[folderPath] = [];
      }

      grouped[folderPath].push({ ...file, nameOnly: nameOnly });
    });

    return grouped;
  }

  const downloadAll = async (ticketId: number) => {
    if (files.length)
    {
      const zip = new JSZip();
      for (const file of files) {
        if(file.id == null) continue;

        const downloadLink = await fileApi.getTicketSignedUrl(ticketId, file.id)
        if(!downloadLink) continue;

        const res = await fetch(downloadLink);
        const blob = await res.blob();

        zip.file(file.fileName, blob);
      }

      const content = await zip.generateAsync({ type: 'blob' });
      const a = document.createElement('a');
      a.href = URL.createObjectURL(content);
      a.download =  data?.title +'.zip';
      a.click();
      URL.revokeObjectURL(a.href);
    }
  }

  if (!data) return <div className="p-4">Đang tải dữ liệu...</div>;

  return (
    <div className="min-h-screen">
      <div className="mx-auto">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6 dark:text-gray-400">
          <span>Trang chủ</span>
          <span>/</span>
          <span>Tickets</span>
          <span>/</span>
          <span className="text-[#3752D8] dark:text-gray-200 font-medium">Chi tiết</span>
        </nav>

        {/* Chat Widget */}
        <ChatWidget ticketId={data.id}/>

        {/* Clean Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6 border-b border-gray-100">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 mb-3">{data.title}</h1>
                <div className="flex items-center gap-4">
                  <span
                    className={clsx(
                      'px-3 py-1 rounded-md text-sm font-medium',
                      statusClass[getOverallStatus(data)] || 'bg-gray-100 text-gray-800'
                    )}
                  >
                    {getOverallStatus(data)}
                  </span>
                  <span className="text-gray-500 text-sm">
                    ID: #{data.id}
                  </span>
                </div>
              </div>
              {
                actions && <div className='flex items-center gap-3'>
                  <button className='bg-[#3752D8] text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors' onClick={() => {handleApproval()}}>
                    Xác nhận
                  </button>
                  <button className='bg-red-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-red-600 transition-colors' onClick={() => {handleReject()}}>
                    Từ chối
                  </button>
                </div>
              }
            </div>
          </div>
        </div>
        {/* Main Content - Better Column Layout */}
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Left Column - Main Content (2/3) */}
          <div className='lg:col-span-2 space-y-6'>
            {/* Tab Navigation */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6" aria-label="Tabs">
                  <button
                    onClick={() => setActiveTab('info')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'info'
                        ? 'border-[#3752D8] text-[#3752D8]'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Thông tin yêu cầu
                  </button>
                  <button
                    onClick={() => setActiveTab('files')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'files'
                        ? 'border-[#3752D8] text-[#3752D8]'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Tài liệu đính kèm
                  </button>
                  {data.ticketStatuses && data.ticketStatuses.some(status => status.additionInfos && status.additionInfos.length > 0) && (
                    <button
                      onClick={() => {
                        setActiveTab('additional');
                        setActiveStepTab(0); // Reset về step đầu tiên
                      }}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'additional'
                          ? 'border-[#3752D8] text-[#3752D8]'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Thông tin bổ sung
                    </button>
                  )}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {/* Thông tin yêu cầu Tab */}
                {activeTab === 'info' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block">Nội dung</label>
                      <p className="text-gray-900">{data.description}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block">Ngày tạo</label>
                      <p className="text-gray-900">{new Date(data.createdAt).toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block">Tool tiếp nhận</label>
                      <p className="text-gray-900">{data.moduleName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 mb-1 block">Người yêu cầu</label>
                      <p className="text-gray-900">{data.createdBy}</p>
                    </div>
                  </div>
                )}

                {/* Tài liệu đính kèm Tab */}
                {activeTab === 'files' && (
                  <div>
                    <input
                        ref={fileInputRef}
                        type="file"
                        className="hidden"
                        onChange={uploadFile}
                        multiple
                    />
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-semibold text-gray-900">Tài liệu đính kèm</h3>
                      <div className='flex gap-3'>
                        <button className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors" onClick={()=>{fileInputRef.current?.click()}}>
                          <span className="text-sm flex items-center gap-2">
                            <FaUpload className='text-sm'/> Tải lên
                          </span>
                        </button>
                        <button className="px-4 py-2 rounded-lg bg-[#3752D8] text-white hover:bg-blue-700 transition-colors" onClick={() => downloadAll(data.id)}>
                          <span className="text-sm">Tải xuống tất cả</span>
                        </button>
                      </div>
                    </div>

                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="bg-gray-50 border-b border-gray-200">
                            <th className="px-6 py-3 text-left font-medium text-gray-700">STT</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-700">Tài liệu</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-700">Thời gian</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-700">Người nộp</th>
                            <th className="px-6 py-3 text-center font-medium text-gray-700">Hành động</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {Object.entries(groupFilesByFolder(files)).map(([folder, filesInFolder]) => (
                            <>
                              <tr key={folder} className="bg-blue-50">
                                <td className="px-6 py-3 font-medium text-[#3752D8]" colSpan={5}>
                                  <div className="flex items-center gap-2">
                                    <span>📁</span>
                                    <span>{folder === 'root' ? 'Tệp gốc' : folder}</span>
                                  </div>
                                </td>
                              </tr>
                              {filesInFolder.map((value: any, index: any) => (
                                <tr key={folder + index} className="hover:bg-gray-50 transition-colors">
                                  <td className="px-6 py-4 text-gray-600">{index + 1}</td>
                                  <td className="px-6 py-4">
                                    <div className="flex items-center gap-2">
                                      <span className="text-gray-400">📄</span>
                                      <span className="text-gray-900">{value.nameOnly}</span>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 text-gray-600">{value.lastModified ? new Date(value.lastModified).toLocaleString() : "Không rõ"}</td>
                                  <td className="px-6 py-4 text-gray-600">{value.user}</td>
                                  <td className="px-6 py-4 text-center">
                                    <button
                                      onClick={() => downloadFile(data.id, value)}
                                      className="p-2 rounded-lg bg-[#3752D8] text-white hover:bg-blue-700 transition-colors"
                                    >
                                      <FaDownload fontSize={14} />
                                    </button>
                                  </td>
                                </tr>
                              ))}
                            </>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Thông tin bổ sung Tab */}
                {activeTab === 'additional' && (() => {
                  // Lọc ra các step có thông tin bổ sung
                  const stepsWithAdditionalInfo = data.ticketStatuses.filter(
                    status => status.additionInfos && status.additionInfos.length > 0
                  );

                  if (stepsWithAdditionalInfo.length === 0) {
                    return (
                      <div className="text-center py-8 text-gray-500">
                        Không có thông tin bổ sung nào
                      </div>
                    );
                  }

                  return (
                    <div>
                      {/* Sub-tab navigation cho các step */}
                      <div className="border-b border-gray-200 mb-6">
                        <nav className="flex space-x-6" aria-label="Step tabs">
                          {stepsWithAdditionalInfo.map((status, index) => (
                            <button
                              key={index}
                              onClick={() => setActiveStepTab(index)}
                              className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                                activeStepTab === index
                                  ? 'border-[#3752D8] text-[#3752D8]'
                                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  status.status === 'Approved' ? 'bg-green-500' :
                                  status.status === 'Reject' ? 'bg-red-500' :
                                  status.status === 'Approving' ? 'bg-yellow-500' : 'bg-gray-500'
                                }`} />
                                <span>{status.name}</span>
                              </div>
                            </button>
                          ))}
                        </nav>
                      </div>

                      {/* Nội dung của step được chọn */}
                      {stepsWithAdditionalInfo[activeStepTab] && (
                        <div>
                          <div className="mb-6">
                            <div className="flex items-center gap-3 mb-2">
                              <div className={`w-3 h-3 rounded-full ${
                                stepsWithAdditionalInfo[activeStepTab].status === 'Approved' ? 'bg-green-500' :
                                stepsWithAdditionalInfo[activeStepTab].status === 'Reject' ? 'bg-red-500' :
                                stepsWithAdditionalInfo[activeStepTab].status === 'Approving' ? 'bg-yellow-500' : 'bg-gray-500'
                              }`} />
                              <h4 className="text-lg font-semibold text-gray-900">
                                {stepsWithAdditionalInfo[activeStepTab].name}
                              </h4>
                            </div>
                            <p className="text-sm text-gray-500 ml-6">
                              Cập nhật: {stepsWithAdditionalInfo[activeStepTab].updatedAt
                                ? new Date(stepsWithAdditionalInfo[activeStepTab].updatedAt).toLocaleString()
                                : new Date(stepsWithAdditionalInfo[activeStepTab].createdAt).toLocaleString()}
                            </p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {stepsWithAdditionalInfo[activeStepTab].additionInfos?.map((info, infoIndex) => (
                              <div key={infoIndex} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <label className="text-sm font-medium text-gray-700 mb-2 block">{info.key}</label>
                                <div className="text-sm text-gray-900 bg-white px-3 py-2 rounded border min-h-[40px] flex items-center">
                                  {info.type === 'file' ? (
                                    <span className="text-[#3752D8] underline cursor-pointer hover:text-blue-700 transition-colors">
                                      {info.value}
                                    </span>
                                  ) : (
                                    <span>{info.value}</span>
                                  )}
                                </div>
                                <span className="text-xs text-gray-400 mt-1 block">Loại: {info.type}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            </div>
          </div>

          {/* Right Column - Timeline (1/3) */}
          <div className='lg:col-span-1'>
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 sticky top-6'>
              <div className="px-6 py-4 border-b border-gray-100">
                <div className="flex items-center gap-2">
                  <FaHistory className='text-[#3752D8] h-5 w-5'/>
                  <h3 className="text-lg font-semibold text-gray-900">Lịch sử xử lý</h3>
                </div>
              </div>
              <div className="p-6">
                {data.ticketStatuses && <Timeline timelineData={data.ticketStatuses}/>}
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}
